from typing import Annotated

from fastapi import Depends
from starlette.requests import Request

from salestech_be.app_context.app_context import LifeSpanService
from salestech_be.common.exception import IllegalStateError
from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.activity.service.activity_service import ActivityService
from salestech_be.core.business_process.sales_methodology.service import (
    BusinessProcessService,
)
from salestech_be.core.calendar.user_calendar_schedule_service import (
    UserCalendarScheduleService,
)
from salestech_be.core.chat.service.chat_history_query_service import (
    ChatHistoryQueryService,
)
from salestech_be.core.chat.service.chat_history_service import ChatHistoryService
from salestech_be.core.chat.service.chat_service import ChatService
from salestech_be.core.citation.service.citation_query_service import (
    CitationQueryService,
)
from salestech_be.core.comment.service.comment_service import CommentService
from salestech_be.core.contact.service.contact_enrichment_service import (
    ContactEnrichmentService,
)
from salestech_be.core.contact.service.contact_query_service import ContactQueryService
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.crm_ai_rec.crm_ai_rec_service import CrmAIRecService
from salestech_be.core.custom_object.service.association_service import (
    AssociationService,
)
from salestech_be.core.custom_object.service.custom_object_query_service import (
    CustomObjectQueryService,
)
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
)
from salestech_be.core.data.service.query_service import DomainObjectQueryService
from salestech_be.core.domain_object_list.service.domain_object_list_query_service import (
    DomainObjectListQueryService,
)
from salestech_be.core.domain_object_list.service.domain_object_list_service import (
    DomainObjectListService,
)
from salestech_be.core.email.account.service_v2 import EmailAccountServiceV2
from salestech_be.core.email.global_email.global_thread_query_service import (
    GlobalThreadQueryService,
)
from salestech_be.core.email.global_email.global_thread_service import (
    GlobalThreadService,
)
from salestech_be.core.email.outbound_domain.service import OutboundDomainService
from salestech_be.core.email.pool.service import EmailAccountPoolService
from salestech_be.core.email.service.imap_syncing_service import ImapSyncingService
from salestech_be.core.event_tracking.event_tracking_receiver_service import (
    EventTrackingReceiverService,
)
from salestech_be.core.event_tracking.event_tracking_service import EventTrackingService
from salestech_be.core.imports.service.crm_sync_service import CrmSyncService
from salestech_be.core.imports.service.import_csv_job_review_service import (
    ImportCsvJobReviewService,
)
from salestech_be.core.job.service.job_service import JobService
from salestech_be.core.meeting.meeting_agent_service import MeetingAgentService
from salestech_be.core.meeting.meeting_share_service import MeetingShareService
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.core.metadata.service.pipeline_stage_service import (
    PipelineStageSelectListService,
)
from salestech_be.core.metadata.service.stage_criteria_service import (
    StageCriteriaService,
)
from salestech_be.core.note.service.note_service import NoteService
from salestech_be.core.notification.service.notification_service import (
    NotificationService,
)
from salestech_be.core.pipeline.service.pipeline_intel_service import (
    PipelineIntelService,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_service import (
    PipelineQualificationPropertyService,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_services import (
    CompetitorService,
    DecisionCriteriaItemService,
    DecisionProcessItemService,
    IdentifiedPainItemService,
    MetricItemService,
    PaperProcessItemService,
)
from salestech_be.core.pipeline.service.pipeline_service import PipelineService
from salestech_be.core.sequence.service.sequence_enrollment_service import (
    SequenceEnrollmentService,
)
from salestech_be.core.sequence.service.sequence_query_service import (
    SequenceQueryService,
)
from salestech_be.core.sequence.service.sequence_service import SequenceService
from salestech_be.core.sequence.service.sequence_step_service import (
    SequenceStepService,
)
from salestech_be.core.stage_criteria.stage_criteria_service_v2 import (
    StageCriteriaServiceV2,
)
from salestech_be.core.task.service.task_v2_service import TaskV2Service
from salestech_be.core.tracker.service.tracker_query_service import TrackerQueryService
from salestech_be.core.tracker.service.tracker_service import TrackerService
from salestech_be.core.user.signature.service import SignatureService
from salestech_be.core.variable.variable_service import VariableService
from salestech_be.core.view_management.service.view_management_service import (
    ViewManagementService,
)
from salestech_be.core.workflow.service.workflow_trigger_service import (
    WorkflowTriggerService,
)
from salestech_be.integrations.kafka.kafka_manager import MSKProducer
from salestech_be.search.search.search_service import SearchService
from salestech_be.web.api.connect.service import UserIntegrationService
from salestech_be.web.api.crm_integrity.service import CRMIntegrityService
from salestech_be.web.api.propagation_rule.service import PropagationRuleService
from salestech_be.web.api.prospecting.company.service import CompanyService
from salestech_be.web.api.prospecting.people.service import PeopleService
from salestech_be.web.api.reporting.dataset.service import ReportingDatasetService
from salestech_be.web.api.user.platform_credential.service import (
    UserPlatformCredentialService,
)
from salestech_be.web.api.user_invite.service import UserInviteService
from salestech_be.web.api.workflow.block.service import WorkflowBlockService
from salestech_be.web.api.workflow.edge.service import WorkflowEdgeService
from salestech_be.web.api.workflow.node.service import WorkflowNodeService
from salestech_be.web.api.workflow.run.service import WorkflowRunService
from salestech_be.web.api.workflow.run_node.service import WorkflowRunNodeService
from salestech_be.web.api.workflow.snapshot.service import WorkflowSnapshotService
from salestech_be.web.api.workflow.workflow.service import WorkflowService


def get_lifespan_service(request: Request) -> LifeSpanService:
    """Retrieves LifeSpanService instance from app context singleton."""
    _life_span_service = request.app.state.app_context.lifespan_service
    if not isinstance(_life_span_service, LifeSpanService):
        raise IllegalStateError(
            f"expected lifespan_service to be of type {LifeSpanService}, "
            f"but got {type(_life_span_service)}"
        )
    return _life_span_service


def pipeline_stage_select_list_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> PipelineStageSelectListService:
    return lifespan_service.pipeline_stage_select_list_service


def stage_criteria_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> StageCriteriaService:
    return lifespan_service.stage_criteria_service


def stage_criteria_service_v2_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> StageCriteriaServiceV2:
    return lifespan_service.stage_criteria_service_v2


def select_list_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> InternalSelectListService:
    return lifespan_service.select_list_service


# def vital_service_from_lifespan(
#     lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
# ) -> VitalService:
#     return lifespan_service.vital_service


def crm_sync_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> CrmSyncService:
    return lifespan_service.crm_sync_service


def job_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> JobService:
    return lifespan_service.job_service


def account_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> AccountService:
    return lifespan_service.account_service


def activity_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> ActivityService:
    return lifespan_service.activity_service


def contact_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> ContactService:
    return lifespan_service.contact_service


def contact_enrichment_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> ContactEnrichmentService:
    return lifespan_service.contact_enrichment_service


def contact_query_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> ContactQueryService:
    return lifespan_service.contact_query_service


def custom_object_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> CustomObjectService:
    return lifespan_service.custom_object_service


def association_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> AssociationService:
    return lifespan_service.association_service


def domain_object_query_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> DomainObjectQueryService:
    return lifespan_service.domain_object_query_service


def domain_object_list_query_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> DomainObjectListQueryService:
    return lifespan_service.domain_object_list_query_service


def event_tracking_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> EventTrackingService:
    return lifespan_service.event_tracking_service


def event_tracking_receiver_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> EventTrackingReceiverService:
    return lifespan_service.event_tracking_receiver_service


def event_tracking_msk_producer_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> MSKProducer:
    return lifespan_service.event_tracking_msk_producer


def view_management_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> ViewManagementService:
    return lifespan_service.view_management_service


def propagation_rule_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> PropagationRuleService:
    return lifespan_service.propagation_rule_service


def people_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> PeopleService:
    return lifespan_service.people_service


def user_integration_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> UserIntegrationService:
    return lifespan_service.user_integration_service


def company_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> CompanyService:
    return lifespan_service.company_service


def variable_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> VariableService:
    return lifespan_service.variable_service


def note_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> NoteService:
    return lifespan_service.note_service


def email_outbound_domain_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> OutboundDomainService:
    return lifespan_service.email_outbound_domain_service


def email_account_pool_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> EmailAccountPoolService:
    return lifespan_service.email_account_pool_service


def email_account_service_v2_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> EmailAccountServiceV2:
    return lifespan_service.email_account_service_v2


def imap_syncing_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> ImapSyncingService:
    return lifespan_service.imap_syncing_service


def meeting_agent_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> MeetingAgentService:
    return lifespan_service.meeting_agent_service


def sequence_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> SequenceService:
    return lifespan_service.sequence_service


def sequence_step_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> SequenceStepService:
    return lifespan_service.sequence_step_service


def user_platform_credential_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> UserPlatformCredentialService:
    return lifespan_service.user_platform_credential_service


def workflow_node_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> WorkflowNodeService:
    return lifespan_service.workflow_node_service


def workflow_snapshot_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> WorkflowSnapshotService:
    return lifespan_service.workflow_snapshot_service


def workflow_block_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> WorkflowBlockService:
    return lifespan_service.workflow_block_service


def workflow_edge_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> WorkflowEdgeService:
    return lifespan_service.workflow_edge_service


def workflow_run_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> WorkflowRunService:
    return lifespan_service.workflow_run_service


def workflow_run_node_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> WorkflowRunNodeService:
    return lifespan_service.workflow_run_node_service


def workflow_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> WorkflowService:
    return lifespan_service.workflow_service


def workflow_trigger_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> WorkflowTriggerService:
    return lifespan_service.workflow_trigger_service


def pipeline_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> PipelineService:
    return lifespan_service.pipeline_service


def meeting_share_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> MeetingShareService:
    return lifespan_service.meeting_share_service


def notification_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> NotificationService:
    return lifespan_service.notification_service


def crm_integrity_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> CRMIntegrityService:
    return lifespan_service.crm_integrity_service


def user_calendar_schedule_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> UserCalendarScheduleService:
    return lifespan_service.user_calendar_schedule_service


def user_invite_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> UserInviteService:
    return lifespan_service.user_invite_service


def tracker_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> TrackerService:
    return lifespan_service.tracker_service


def tracker_query_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> TrackerQueryService:
    return lifespan_service.tracker_query_service


def pipeline_intel_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> PipelineIntelService:
    return lifespan_service.pipeline_intel_service


def domain_object_list_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> DomainObjectListService:
    return lifespan_service.domain_object_list_service


def custom_object_query_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> CustomObjectQueryService:
    return lifespan_service.custom_object_query_service


def search_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> SearchService:
    return lifespan_service.search_service


def chat_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> ChatService:
    return lifespan_service.chat_service


def chat_history_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> ChatHistoryService:
    return lifespan_service.chat_history_service


def chat_history_query_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> ChatHistoryQueryService:
    return lifespan_service.chat_history_query_service


def citation_query_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> CitationQueryService:
    return lifespan_service.citation_query_service


def sequence_query_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> SequenceQueryService:
    return lifespan_service.sequence_query_service


def sequence_enrollment_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> SequenceEnrollmentService:
    return lifespan_service.sequence_enrollment_service


def business_process_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> BusinessProcessService:
    return lifespan_service.business_process_service


def signature_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> SignatureService:
    return lifespan_service.signature_service


def pipeline_qualification_property_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> PipelineQualificationPropertyService:
    return lifespan_service.pipeline_qualification_property_service


def task_v2_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> TaskV2Service:
    return lifespan_service.task_v2_service


def comment_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> CommentService:
    return lifespan_service.comment_service


def global_thread_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> GlobalThreadService:
    return lifespan_service.global_thread_service


def global_thread_query_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> GlobalThreadQueryService:
    return lifespan_service.global_thread_query_service


def crm_ai_rec_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> CrmAIRecService:
    return lifespan_service.crm_ai_rec_service


def identified_pain_item_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> IdentifiedPainItemService:
    return lifespan_service.identified_pain_item_service


def decision_criteria_item_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> DecisionCriteriaItemService:
    return lifespan_service.decision_criteria_item_service


def metric_item_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> MetricItemService:
    return lifespan_service.metric_item_service


def paper_process_item_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> PaperProcessItemService:
    return lifespan_service.paper_process_item_service


def competitor_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> CompetitorService:
    return lifespan_service.competitor_service


def decision_process_item_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> DecisionProcessItemService:
    return lifespan_service.decision_process_item_service


def import_csv_job_review_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> ImportCsvJobReviewService:
    return lifespan_service.import_csv_job_review_service


def reporting_dataset_service_from_lifespan(
    lifespan_service: Annotated[LifeSpanService, Depends(get_lifespan_service)],
) -> ReportingDatasetService:
    return lifespan_service.reporting_dataset_service