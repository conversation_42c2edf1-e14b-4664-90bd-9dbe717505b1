from uuid import UUID, uuid4

from fastapi import Request

from salestech_be.common.exception import InvalidArgumentError, ResourceNotFoundError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.type.patch_request import specified
from salestech_be.db.dao.reporting_dataset_repository import ReportingDatasetRepository
from salestech_be.db.dao.reporting_dataset_repository import ReportingDatasetRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.reporting import (
    ReportingDataset,
    ReportingDatasetUpdate,
    ReportingDashboard,
    ReportingDashboardDatasetAssociation,
    ReportingDashboardDatasetAssociationUpdate,
    ReportingDataset,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.reporting.dataset.schema import (
    CreateReportingDatasetRequest,
    CreateReportingDashboardDatasetAssociationRequest,
    PatchReportingDatasetRequest,
    ReportingDatasetDTO,
)

logger = get_logger(__name__)


class ReportingDatasetService:
    def __init__(
        self,
        reporting_dataset_repository: ReportingDatasetRepository,
    ):
        self.reporting_dataset_repository = reporting_dataset_repository


def get_reporting_dataset_service(
    request: Request,
) -> ReportingDatasetService:
    db_engine = get_db_engine(request=request)
    return ReportingDatasetService(
        reporting_dataset_repository=ReportingDatasetRepository(engine=db_engine),
    )


def get_reporting_dataset_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> ReportingDatasetService:
    return ReportingDatasetService(
        reporting_dataset_repository=ReportingDatasetRepository(engine=db_engine),
    )
